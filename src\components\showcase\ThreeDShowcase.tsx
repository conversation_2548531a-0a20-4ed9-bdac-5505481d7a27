'use client'

import React, { Suspense, useRef, useState, useEffect } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { useGLTF, Html, Environment, PresentationControls } from '@react-three/drei'
import { motion } from 'framer-motion'
import { Smartphone, Zap } from 'lucide-react'
import { useLanguage } from '@/contexts/LanguageContext'



function FloatingSmartphone() {
  const meshRef = useRef<any>()
  const { scene } = useGLTF('/3d/Smartphone .glb')
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 1.2) * 0.2
    }
  })
  
  return (
    <primitive 
      ref={meshRef}
      object={scene.clone()} 
      position={[0, 0, 0]} 
      scale={0.8}
    />
  )
}

// Y2K models removed - keeping only smartphone

function LoadingSpinner() {
  return (
    <Html center>
      <div className="flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ocean-green"></div>
        <span className="ml-2 text-ocean-green">Loading 3D...</span>
      </div>
    </Html>
  )
}

function ActiveModel({ modelType }: { modelType: number }) {
  useEffect(() => {
    console.log(`3D Model changed to type: ${modelType}`)
  }, [modelType])

  // Only smartphone model available now
  return <FloatingSmartphone />
}

function ThreeDShowcase() {
  const { t } = useLanguage()
  const [activeModel] = useState(0)
  const [isMobile, setIsMobile] = useState(false)
  const [isInteracting, setIsInteracting] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const checkMobile = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const isSmallScreen = window.innerWidth <= 768
      setIsMobile(isTouchDevice && isSmallScreen)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // FIXED: Better touch event handling that doesn't interfere with global scrolling
  useEffect(() => {
    const container = containerRef.current
    if (!container || !isMobile) return

    let isUserInteracting = false
    let touchStartY = 0
    let touchStartX = 0
    let isDragging = false

    const handleTouchStart = (e: TouchEvent) => {
      const touch = e.touches[0]
      touchStartY = touch.clientY
      touchStartX = touch.clientX
      isDragging = false
      
      // Only start interaction if touch is directly on the canvas or 3D container
      const isOnCanvas = (e.target as Element)?.closest('canvas') || (e.target as Element)?.closest('.threejs-container')
      
      if (isOnCanvas) {
        isUserInteracting = true
        setIsInteracting(true)
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isUserInteracting) return
      
      const touch = e.touches[0]
      const deltaY = Math.abs(touch.clientY - touchStartY)
      const deltaX = Math.abs(touch.clientX - touchStartX)
      
      // Only prevent default if user is clearly dragging horizontally (3D rotation)
      // Allow vertical scrolling (smaller deltaX, larger deltaY)
      if (deltaX > deltaY && deltaX > 10) {
        isDragging = true
        e.preventDefault() // Prevent scrolling only when dragging horizontally
        e.stopPropagation()
      } else if (deltaY > 10 && !isDragging) {
        // User is scrolling vertically, allow it
        isUserInteracting = false
        setIsInteracting(false)
      }
    }

    const handleTouchEnd = () => {
      isUserInteracting = false
      setIsInteracting(false)
      isDragging = false
      touchStartY = 0
      touchStartX = 0
    }

    // IMPORTANT: Use capture phase to handle events before they bubble
    container.addEventListener('touchstart', handleTouchStart, { passive: false, capture: true })
    container.addEventListener('touchmove', handleTouchMove, { passive: false, capture: true })
    container.addEventListener('touchend', handleTouchEnd, { passive: false, capture: true })

    return () => {
      container.removeEventListener('touchstart', handleTouchStart, true)
      container.removeEventListener('touchmove', handleTouchMove, true)
      container.removeEventListener('touchend', handleTouchEnd, true)
    }
  }, [isMobile])

  const models = [
    {
      name: t('threej.mobile_first'),
      description: t('threej.mobile_description'),
      icon: <Smartphone className="w-6 h-6" />
    }
  ]

  return (
    <section 
      data-section="threej-showcase"
      className="py-20 lg:py-32 px-6 bg-gradient-to-br from-background-primary via-background-secondary to-background-tertiary relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-fun-blue/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-astronaut/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <motion.div
            className="inline-flex items-center gap-2 bg-gradient-to-r from-fun-blue/10 to-astronaut/10 border border-fun-blue/20 rounded-full px-6 py-3 mb-6"
            whileHover={{ scale: 1.05 }}
          >
            <Zap className="w-5 h-5 text-fun-blue" />
            <span className="text-fun-blue font-medium">{t('threej.innovation')}</span>
          </motion.div>

          <h2 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-fun-blue to-astronaut bg-clip-text text-transparent">
              {t('threej.title')}
            </span>
          </h2>
          
          <p className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
            {t('threej.description')}
          </p>
        </motion.div>

        {/* Mobile Design Showcase */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Description */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-text-primary mb-6">{t('threej.interactive_models')}</h3>
            <div className="bg-gradient-to-r from-fun-blue/10 to-astronaut/10 border border-fun-blue/30 p-6 rounded-xl">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-lg bg-fun-blue text-white">
                  <Smartphone className="w-6 h-6" />
                </div>
                <div>
                  <h4 className="font-semibold text-fun-blue text-lg mb-2">
                    {models[0].name}
                  </h4>
                  <p className="text-text-secondary leading-relaxed">
                    {models[0].description}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* 3D Canvas */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div 
              ref={containerRef}
              className="relative h-96 md:h-[500px] bg-gradient-to-br from-background-secondary to-background-tertiary rounded-2xl border border-white/10 overflow-hidden threejs-container"
              style={{
                // FIXED: Better touch handling
                touchAction: isMobile ? 'pan-y pinch-zoom' : 'auto', // Allow vertical panning (scroll) but prevent horizontal
                userSelect: 'none',
                WebkitUserSelect: 'none',
                position: 'relative'
              }}
            >
              <Canvas
                camera={{ position: [0, 0, 5], fov: 60 }}
                gl={{ antialias: true, alpha: true }}
                dpr={[1, 2]}
                // FIXED: Add event handling configuration
                onCreated={(state) => {
                  // Disable default Three.js touch handling on mobile
                  if (isMobile) {
                    state.gl.domElement.style.touchAction = 'none'
                  }
                }}
              >
                <Suspense fallback={<LoadingSpinner />}>
                  {/* Lighting */}
                  <ambientLight intensity={0.5} />
                  <pointLight position={[10, 10, 10]} intensity={1} />
                  <pointLight position={[-10, -10, -10]} intensity={0.5} color="#4A90E2" />
                  
                  {/* Environment */}
                  <Environment preset="studio" />
                  
                  {/* Active Model with controlled interaction */}
                  <PresentationControls
                    global={false}
                    snap={true}
                    rotation={[0, 0.3, 0]}
                    polar={[-Math.PI / 4, Math.PI / 4]}
                    azimuth={[-Math.PI / 2, Math.PI / 2]}
                    config={{ mass: 2, tension: 400 }}
                    // FIXED: Better mobile controls
                    enabled={!isMobile || isInteracting} // Disable controls when not interacting on mobile
                  >
                    <ActiveModel modelType={activeModel} />
                  </PresentationControls>
                </Suspense>
              </Canvas>
              
              {/* Interaction Hint */}
              <div className="absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 pointer-events-none">
                <p className="text-white text-sm">
                  {isMobile ? '👆 Tap & drag horizontally' : '🖱️ Click & drag to explore'}
                </p>
              </div>

              {/* Mobile interaction overlay */}
              {isMobile && isInteracting && (
                <div className="absolute inset-0 pointer-events-none">
                  <div className="absolute top-4 left-4 bg-fun-blue/80 backdrop-blur-sm rounded-lg px-3 py-2">
                    <p className="text-white text-sm font-medium">
                      🔄 Rotating 3D model
                    </p>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          {[
            {
              title: 'Mobile Performance',
              description: 'Geoptimaliseerd voor snelle laadtijden op alle mobiele apparaten'
            },
            {
              title: 'Responsive Design',
              description: 'Perfecte weergave op smartphones, tablets en desktop'
            },
            {
              title: 'User Experience',
              description: 'Intuïtieve navigatie en optimale conversie op elk apparaat'
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="text-center p-6 bg-background-secondary rounded-xl border border-white/10"
              whileHover={{ scale: 1.05, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <h4 className="text-lg font-semibold text-text-primary mb-2">
                {feature.title}
              </h4>
              <p className="text-text-secondary">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export { ThreeDShowcase } 