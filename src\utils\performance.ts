/**
 * Performance optimization utilities
 */

import React, { lazy, ComponentType } from 'react'

// Lazy loading with better error handling
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  return lazy(async () => {
    try {
      const moduleResult = await importFn()
      return moduleResult
    } catch (error) {
      console.error('Failed to load component:', error)
      // Return a fallback component if loading fails
      if (fallback) {
        return { default: fallback }
      }
      // Return a minimal error component
      const ErrorComponent = () => {
        return React.createElement('div', {
          className: 'p-4 text-center text-red-400'
        }, 'Failed to load component')
      }
      return { default: ErrorComponent as unknown as T }
    }
  })
}

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func(...args)
  }
}

// Throttle function for scroll and resize events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean

  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

// Intersection Observer for lazy loading
export function createIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }

  return new IntersectionObserver(callback, defaultOptions)
}

// Image preloading utility
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

// Preload multiple images
export async function preloadImages(srcs: string[]): Promise<void[]> {
  return Promise.all(srcs.map(preloadImage))
}

// Resource hints for better loading performance
export function addResourceHint(
  href: string,
  rel: 'preload' | 'prefetch' | 'preconnect' | 'dns-prefetch',
  as?: string,
  type?: string
): void {
  if (typeof document === 'undefined') return

  // Check if hint already exists
  const existing = document.querySelector(`link[href="${href}"][rel="${rel}"]`)
  if (existing) return

  const link = document.createElement('link')
  link.rel = rel
  link.href = href
  
  if (as) link.setAttribute('as', as)
  if (type) link.type = type
  
  document.head.appendChild(link)
}

// Preconnect to external domains
export function preconnectToDomain(domain: string): void {
  addResourceHint(domain, 'preconnect')
}

// DNS prefetch for external domains
export function dnsPrefetch(domain: string): void {
  addResourceHint(domain, 'dns-prefetch')
}

// Memory usage monitoring (development only)
export function logMemoryUsage(): void {
  if (process.env.NODE_ENV !== 'development') return
  if (!('memory' in performance)) return

  const memory = (performance as any).memory
  console.log('Memory Usage:', {
    used: `${Math.round(memory.usedJSHeapSize / 1048576)} MB`,
    total: `${Math.round(memory.totalJSHeapSize / 1048576)} MB`,
    limit: `${Math.round(memory.jsHeapSizeLimit / 1048576)} MB`
  })
}

// Performance timing utilities
export function measurePerformance<T>(
  name: string,
  fn: () => T
): T {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  
  console.log(`${name} took ${end - start} milliseconds`)
  return result
}

export async function measureAsyncPerformance<T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  
  console.log(`${name} took ${end - start} milliseconds`)
  return result
}

// Bundle size optimization helpers
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development'
}

export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production'
}

// Critical resource loading
export function loadCriticalCSS(href: string): void {
  addResourceHint(href, 'preload', 'style')
}

export function loadCriticalJS(href: string): void {
  addResourceHint(href, 'preload', 'script')
}

// Viewport-based loading
export function isInViewport(element: Element): boolean {
  const rect = element.getBoundingClientRect()
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  )
}

// Service Worker utilities
export function registerServiceWorker(swPath: string = '/sw.js'): Promise<ServiceWorkerRegistration | null> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return Promise.resolve(null)
  }

  return navigator.serviceWorker
    .register(swPath)
    .then((registration) => {
      console.log('SW registered: ', registration)
      return registration
    })
    .catch((registrationError) => {
      console.log('SW registration failed: ', registrationError)
      return null
    })
}

// Web Vitals tracking
export function trackWebVitals(): void {
  if (typeof window === 'undefined') return

  // Track CLS (Cumulative Layout Shift)
  const clsEntries: PerformanceEntry[] = []

  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
        clsEntries.push(entry)
      }
    }
  })

  observer.observe({ type: 'layout-shift', buffered: true })

  // Track LCP (Largest Contentful Paint)
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    const lastEntry = entries[entries.length - 1]
    console.log('LCP:', lastEntry.startTime)
  })

  lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true })

  // Track FID (First Input Delay)
  const fidObserver = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      console.log('FID:', (entry as PerformanceEventTiming).processingStart - entry.startTime)
    }
  })

  fidObserver.observe({ type: 'first-input', buffered: true })
}

// Export performance configuration
export const performanceConfig = {
  // Intersection Observer thresholds
  intersectionThresholds: [0, 0.25, 0.5, 0.75, 1],
  
  // Debounce/throttle delays
  scrollDelay: 16, // ~60fps
  resizeDelay: 150,
  searchDelay: 300,
  
  // Image loading
  imageLoadingThreshold: 0.1,
  imageRootMargin: '50px',
  
  // Bundle splitting
  chunkSizeLimit: 244 * 1024, // 244KB
  
  // Cache durations
  staticCacheDuration: 31536000, // 1 year
  dynamicCacheDuration: 86400, // 1 day
} as const
