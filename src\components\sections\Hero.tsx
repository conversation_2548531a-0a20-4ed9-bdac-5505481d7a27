'use client';

import React, { useCallback } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>rk<PERSON>, ArrowDown } from 'lucide-react';
import Image from 'next/image';
import { GlareButton } from '../ui/glare-button';
import { WhatsAppActions } from '@/utils/whatsapp';
import { useLanguage } from '@/contexts/LanguageContext';
import { FacebookTracking } from '@/utils/facebook-tracking';
import { useMobile } from '@/hooks/use-mobile';

const Hero = React.memo(function Hero() {
  const { t } = useLanguage();
  const isMobile = useMobile();

  // Memoized event handlers
  const handleViewWork = useCallback(() => {
    FacebookTracking.trackViewWork();
    const portfolioSection = document.querySelector('[data-section="portfolio"]');
    if (portfolioSection) {
      portfolioSection.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  const handlePlanMeeting = useCallback(() => {
    FacebookTracking.trackPlanMeeting();
    WhatsAppActions.planMeeting();
  }, []);

  // Mobile-specific animation variants - simplified and optimized
  const mobileStarVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0,
      rotate: -180
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      rotate: 0,
      transition: {
        delay: 0.3,
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const mobileLogoVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.5,
      y: 30
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        delay: 0.6,
        duration: 0.8,
        ease: [0.165, 0.84, 0.44, 1]
      }
    }
  };

  // Brand new mobile-optimized welcome text animation
  const mobileWelcomeVariants = {
    hidden: { 
      opacity: 0,
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: 0.9,
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  // Desktop animation variants - more complex for better UX
  const desktopStarVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.5 }
    }
  };

  const desktopLogoVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.6, delay: 0.2 }
    }
  };

  // Desktop wave animation (keeps the complex wave effect)
  const desktopWaveVariants = {
    hidden: { 
      y: 30, 
      opacity: 0,
      scale: 0.8
    },
    visible: (i: number) => ({
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        delay: 0.4 + (i * 0.03),
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: 'spring',
        damping: 20,
        stiffness: 120
      }
    })
  };

  // Simple mobile text component - NO character splitting
  const MobileWelcomeText = () => (
    <motion.div 
      className="hero-welcome mb-2"
      variants={mobileWelcomeVariants}
      initial="hidden"
      animate="visible"
    >
      {t('hero.welcome')}
    </motion.div>
  );

  // Complex desktop wave animation - character splitting for desktop only
  const DesktopWaveText = ({ text }: { text: string }) => {
    return text.split('').map((char, index) => (
      <motion.span
        key={index}
        custom={index}
        variants={desktopWaveVariants}
        initial="hidden"
        animate="visible"
        className="inline-block"
        style={char === ' ' ? { width: '0.3em' } : {}}
      >
        {char === ' ' ? '\u00A0' : char}
      </motion.span>
    ));
  };

  return (
    <section 
      data-section="hero"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background-primary via-background-secondary to-background-tertiary pt-16 sm:pt-20 md:pt-0"
      style={{ contain: 'layout style paint' }} // Optimization for mobile scrolling
    >
      {/* Optimized background with better mobile performance */}
      <div className="absolute inset-0 bg-gradient-to-br from-ocean-green/10 via-background-primary to-eucalyptus/10">
        {/* Dynamic Gradient Waves */}
        <div className="dynamic-gradient" />
        <div className="dynamic-gradient-2" />
        
        {/* Reduced particles for mobile performance */}
        <div className="floating-particles">
          {/* Fewer particles on mobile */}
          {Array.from({ length: isMobile ? 3 : 6 }).map((_, i) => (
            <div
              key={`small-${i}`}
              className="particle particle-small"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 12}s`
              }}
            />
          ))}
          
          {Array.from({ length: isMobile ? 2 : 4 }).map((_, i) => (
            <div
              key={`medium-${i}`}
              className="particle particle-medium"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 16}s`
              }}
            />
          ))}
          
          {!isMobile && Array.from({ length: 2 }).map((_, i) => (
            <div
              key={`large-${i}`}
              className="particle particle-large"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 20}s`
              }}
            />
          ))}
          
          {Array.from({ length: isMobile ? 1 : 2 }).map((_, i) => (
            <div
              key={`glow-${i}`}
              className="particle particle-glow"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 14}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Hero content with optimized containment */}
      <div className="relative z-10 text-center section-padding container-center" style={{ contain: 'layout style paint' }}>
        {/* Star Animation - Device-specific variants */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={isMobile ? mobileStarVariants : desktopStarVariants}
          className="mb-6"
        >
          <Sparkles className="w-16 h-16 text-ocean-green mx-auto animate-pulse-neon" />
        </motion.div>

        {/* Logo Animation - Device-specific variants */}
        <motion.div 
          className="mb-8 logo-sparkles"
          initial="hidden"
          animate="visible"
          variants={isMobile ? mobileLogoVariants : desktopLogoVariants}
        >
          <Image 
            src="/img/Logo-Wide.png" 
            alt="GrowInity - Specialist professionele website laten maken WordPress Nederland" 
            width={400}
            height={100}
            priority
            className="h-16 sm:h-20 lg:h-24 mx-auto mb-4 logo-animated cursor-pointer w-auto"
            style={{
              imageRendering: 'crisp-edges'
            }}
          />
        </motion.div>

        {/* Title - Completely different approach for mobile vs desktop */}
        <motion.h1 
          className="hero-title heading-xl mb-6 text-balance"
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
        >
          {/* Mobile: Simple, smooth animation */}
          {isMobile ? (
            <MobileWelcomeText />
          ) : (
            /* Desktop: Complex wave animation */
            <div className="hero-welcome mb-2">
              <DesktopWaveText text={t('hero.welcome')} />
            </div>
          )}
          
          {/* Company name - same for both but different timing */}
          <motion.div 
            className="hero-company"
            initial={{ opacity: 0, scale: 0.8, y: 30 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{
              delay: isMobile ? 1.2 : 1.0, // Slightly more delay on mobile for sequence
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: 'spring',
              damping: 15,
              stiffness: 100
            }}
          >
            {t('hero.company')}
          </motion.div>
        </motion.h1>

        {/* Subtitle - H2 for better SEO hierarchy */}
        <motion.h2 
          className="hero-subtitle text-xl sm:text-2xl text-text-secondary mb-4 text-balance"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            delay: isMobile ? 1.5 : 0.8, 
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
          dangerouslySetInnerHTML={{ __html: t('hero.subtitle') }}
        />

        {/* Description - optimized timing for mobile */}
        <motion.p 
          className="hero-description text-lg text-text-muted mb-8 max-w-2xl mx-auto text-balance"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            delay: isMobile ? 1.8 : 1.0, 
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
          dangerouslySetInnerHTML={{ __html: t('hero.description') }}
        />

        {/* CTA Buttons - optimized timing for mobile */}
        <motion.div 
          className="hero-cta button-group-mobile mb-16 sm:mb-12 lg:mb-8"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ 
            delay: isMobile ? 2.1 : 1.2, 
            duration: 0.5, 
            ease: [0.175, 0.885, 0.32, 1.275]
          }}
        >
          <GlareButton size="lg" onClick={handleViewWork}>
            {t('hero.view_work')}
            <ArrowDown className="w-5 h-5" />
          </GlareButton>
          
          <GlareButton variant="secondary" size="lg" onClick={handlePlanMeeting}>
            {t('hero.plan_meeting')}
            <Sparkles className="w-5 h-5" />
          </GlareButton>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{
          y: [0, 10, 0]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      >
        <ArrowDown className="w-6 h-6 text-ocean-green opacity-60" />
      </motion.div>
    </section>
  );
});

export { Hero };