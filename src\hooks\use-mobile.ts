'use client'

import { useState, useEffect } from 'react'

/**
 * Custom hook for mobile detection with proper SSR handling
 * @returns boolean indicating if the current device is mobile
 */
export function useMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      if (typeof window === 'undefined') return false
      
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const isSmallScreen = window.innerWidth <= 768
      const userAgent = navigator.userAgent || navigator.vendor
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent)
      
      return (isTouchDevice && isSmallScreen) || isMobileAgent
    }
    
    setIsMobile(checkMobile())
    
    const handleResize = () => {
      setIsMobile(checkMobile())
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return isMobile
}

/**
 * Alternative hook that returns both mobile state and screen width
 * @returns object with isMobile boolean and screenWidth number
 */
export function useResponsive() {
  const [state, setState] = useState({
    isMobile: false,
    screenWidth: 0
  })

  useEffect(() => {
    const updateState = () => {
      if (typeof window === 'undefined') return
      
      const width = window.innerWidth
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const userAgent = navigator.userAgent || navigator.vendor
      const isMobileAgent = /android|iphone|ipad|ipod|mobile/i.test(userAgent)
      
      setState({
        isMobile: (isTouchDevice && width <= 768) || isMobileAgent,
        screenWidth: width
      })
    }
    
    updateState()
    
    window.addEventListener('resize', updateState)
    return () => window.removeEventListener('resize', updateState)
  }, [])

  return state
}
